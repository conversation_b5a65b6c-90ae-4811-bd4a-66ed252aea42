//+------------------------------------------------------------------+
//|                                           GridTrendMultiplier.mq5 |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"
#property strict

//--- Input parameters
enum ENUM_DIRECTION { DIR_BUY = 0, DIR_SELL = 1 };

input ENUM_DIRECTION  Direction          = DIR_BUY;     // Trading direction
input double          LotSize            = 0.10;        // Fixed lot size per leg
input int             GridSpacingPips    = 20;          // Pull-back distance to add new leg
input int             TakeProfitPips     = 20;          // TP per leg (same as your "cash-in distance")
input int             MaxGrids           = 10;          // Max concurrent legs
input double          ProfitTargetPct    = 5.0;         // Pause EA after this equity-gain %
input int             SessionStartHour   = 0;           // Trading window start (server hour)
input int             SessionEndHour     = 23;          // Trading window end   (server hour)
input uint            Magic              = 555555;      // Magic number

//--- Global variables
double InitialBalance = 0.0;
double ReferencePrice = 0.0;
bool   EAPaused = false;
int    CurrentGridCount = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    InitialBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    EAPaused = false;
    CurrentGridCount = 0;
    ReferencePrice = 0.0;
    
    Print("Grid Trend Multiplier EA initialized");
    Print("Direction: ", (Direction == DIR_BUY) ? "BUY" : "SELL");
    Print("Initial Balance: ", InitialBalance);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("Grid Trend Multiplier EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if EA should be paused due to profit target
    CheckProfitTarget();
    
    // Check trading session
    if (!IsInTradingSession())
        return;
    
    // Update current grid count
    UpdateGridCount();
    
    // Check for TP hits and recycle positions
    CheckAndRecyclePositions();
    
    // If no positions exist, open seed trade
    if (CurrentGridCount == 0 && !EAPaused)
    {
        OpenSeedTrade();
        return;
    }
    
    // Check for pullback and add new grid leg if conditions are met
    if (!EAPaused && CurrentGridCount < MaxGrids)
    {
        CheckAndAddGridLeg();
    }
}

//+------------------------------------------------------------------+
//| Check if current time is within trading session                 |
//+------------------------------------------------------------------+
bool IsInTradingSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;
    
    if (SessionStartHour <= SessionEndHour)
    {
        // Normal session (e.g., 8 to 17)
        return (currentHour >= SessionStartHour && currentHour <= SessionEndHour);
    }
    else
    {
        // Overnight session (e.g., 22 to 6)
        return (currentHour >= SessionStartHour || currentHour <= SessionEndHour);
    }
}

//+------------------------------------------------------------------+
//| Check profit target and pause EA if reached                     |
//+------------------------------------------------------------------+
void CheckProfitTarget()
{
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equityGain = ((currentBalance - InitialBalance) / InitialBalance) * 100.0;
    
    if (equityGain >= ProfitTargetPct)
    {
        if (!EAPaused)
        {
            EAPaused = true;
            Print("Profit target reached! EA paused. Equity gain: ", DoubleToString(equityGain, 2), "%");
        }
    }
}

//+------------------------------------------------------------------+
//| Update current grid count                                        |
//+------------------------------------------------------------------+
void UpdateGridCount()
{
    CurrentGridCount = 0;
    
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                CurrentGridCount++;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check and recycle positions that hit TP                         |
//+------------------------------------------------------------------+
void CheckAndRecyclePositions()
{
    double currentPrice = (Direction == DIR_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double pipValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if (SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
        pipValue *= 10;
    
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                double lots = PositionGetDouble(POSITION_VOLUME);
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                
                bool shouldClose = false;
                
                if (Direction == DIR_BUY && posType == POSITION_TYPE_BUY)
                {
                    if (currentPrice >= openPrice + (TakeProfitPips * pipValue))
                        shouldClose = true;
                }
                else if (Direction == DIR_SELL && posType == POSITION_TYPE_SELL)
                {
                    if (currentPrice <= openPrice - (TakeProfitPips * pipValue))
                        shouldClose = true;
                }
                
                if (shouldClose)
                {
                    ulong ticket = PositionGetInteger(POSITION_TICKET);
                    if (ClosePosition(ticket))
                    {
                        Print("Position recycled at TP. Ticket: ", ticket);
                        // Immediately reopen identical position
                        if (!EAPaused)
                        {
                            OpenMarketOrder(Direction, lots);
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open seed trade                                                  |
//+------------------------------------------------------------------+
void OpenSeedTrade()
{
    if (OpenMarketOrder(Direction, LotSize))
    {
        Print("Seed trade opened. Direction: ", (Direction == DIR_BUY) ? "BUY" : "SELL");
        UpdateReferencePrice();
    }
}

//+------------------------------------------------------------------+
//| Check and add grid leg on pullback                              |
//+------------------------------------------------------------------+
void CheckAndAddGridLeg()
{
    UpdateReferencePrice();

    if (ReferencePrice == 0.0)
        return;

    double currentPrice = (Direction == DIR_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double pipValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if (SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
        pipValue *= 10;

    bool shouldAddLeg = false;

    if (Direction == DIR_BUY)
    {
        // For BUY: add leg when price retreats (goes down) from reference
        if (currentPrice <= ReferencePrice - (GridSpacingPips * pipValue))
            shouldAddLeg = true;
    }
    else
    {
        // For SELL: add leg when price retreats (goes up) from reference
        if (currentPrice >= ReferencePrice + (GridSpacingPips * pipValue))
            shouldAddLeg = true;
    }

    if (shouldAddLeg)
    {
        if (OpenMarketOrder(Direction, LotSize))
        {
            Print("Grid leg added. Current grid count: ", CurrentGridCount + 1);
            UpdateReferencePrice();
        }
    }
}

//+------------------------------------------------------------------+
//| Update reference price                                           |
//+------------------------------------------------------------------+
void UpdateReferencePrice()
{
    ReferencePrice = 0.0;
    bool firstPosition = true;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);

                if (firstPosition)
                {
                    ReferencePrice = openPrice;
                    firstPosition = false;
                }
                else
                {
                    if (Direction == DIR_BUY)
                    {
                        // For BUY: reference is lowest open price
                        if (openPrice < ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                    else
                    {
                        // For SELL: reference is highest open price
                        if (openPrice > ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open market order                                                |
//+------------------------------------------------------------------+
bool OpenMarketOrder(ENUM_DIRECTION dir, double lots)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lots;
    request.type = (dir == DIR_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    request.price = (dir == DIR_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    request.deviation = 10;
    request.magic = Magic;
    request.comment = "GridTrendMultiplier";

    if (!OrderSend(request, result))
    {
        Print("OrderSend failed. Error: ", GetLastError());
        return false;
    }

    if (result.retcode == TRADE_RETCODE_DONE)
    {
        Print("Order opened successfully. Ticket: ", result.order);
        return true;
    }
    else
    {
        Print("OrderSend failed. Return code: ", result.retcode);
        return false;
    }
}

//+------------------------------------------------------------------+
//| Close position by ticket                                         |
//+------------------------------------------------------------------+
bool ClosePosition(ulong ticket)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    if (!PositionSelectByTicket(ticket))
        return false;

    request.action = TRADE_ACTION_DEAL;
    request.symbol = PositionGetString(POSITION_SYMBOL);
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
    request.position = ticket;
    request.price = (request.type == ORDER_TYPE_SELL) ? SymbolInfoDouble(request.symbol, SYMBOL_BID) : SymbolInfoDouble(request.symbol, SYMBOL_ASK);
    request.deviation = 10;
    request.magic = Magic;

    if (!OrderSend(request, result))
    {
        Print("Close order failed. Error: ", GetLastError());
        return false;
    }

    if (result.retcode == TRADE_RETCODE_DONE)
    {
        return true;
    }
    else
    {
        Print("Close order failed. Return code: ", result.retcode);
        return false;
    }
}
