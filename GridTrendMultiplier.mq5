//+------------------------------------------------------------------+
//|                                           GridTrendMultiplier.mq5 |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"
#property strict

//--- Input parameters
enum ENUM_DIRECTION { DIR_BUY = 0, DIR_SELL = 1 };
enum ENUM_DIRMODE { MANUAL_BUY = 0, MANUAL_SELL = 1, AUTO = 2 };

input ENUM_DIRMODE    DirectionMode      = AUTO;        // BUY / SELL / AUTO
input double          LotSize            = 0.10;        // Fixed lot size per leg
input int             GridSpacePips      = 20;          // Grid space (pullback distance & take profit)
input int             MaxGrids           = 10;          // Max concurrent legs
input double          DailyTargetUSD     = 66.0;        // Stop-trading profit target
input int             TargetResetHour    = 0;           // Server-time hour at which "today" starts
input int             ADX_Threshold      = 20;          // Minimum H4-ADX for trend validity
input bool            CloseAllEOD        = false;       // Optional end-of-day flatten
input int             SessionStartHour   = 0;           // Trading window start (server hour)
input int             SessionEndHour     = 23;          // Trading window end   (server hour)
input uint            Magic              = 555555;      // Magic number

//--- Global variables
double InitialBalance = 0.0;
double ReferencePrice = 0.0;
bool   EAPaused = false;
int    CurrentGridCount = 0;

//--- Daily bias and profit tracking
int    g_dayBias = 0;              // -1=SELL, +1=BUY, 0=FLAT
double g_todayProfitClosed = 0.0;  // Sum of closed profits today
bool   g_exitMode = false;         // EXIT-ONLY state
bool   g_pausedForDay = false;     // Paused until next reset
ulong  g_anchorTicket = 0;         // Oldest ticket of today
datetime g_lastResetTime = 0;      // Last reset timestamp

//--- Indicator handles
int    h_ema200_daily = INVALID_HANDLE;
int    h_adx_h4 = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    InitialBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    EAPaused = false;
    CurrentGridCount = 0;
    ReferencePrice = 0.0;
    g_dayBias = 0;
    g_todayProfitClosed = 0.0;
    g_exitMode = false;
    g_pausedForDay = false;
    g_anchorTicket = 0;
    g_lastResetTime = 0;

    // Initialize indicators for AUTO mode
    if (DirectionMode == AUTO)
    {
        h_ema200_daily = iMA(Symbol(), PERIOD_D1, 200, 0, MODE_EMA, PRICE_CLOSE);
        h_adx_h4 = iADX(Symbol(), PERIOD_H4, 14);

        if (h_ema200_daily == INVALID_HANDLE || h_adx_h4 == INVALID_HANDLE)
        {
            Print("Failed to initialize indicators for AUTO mode");
            return(INIT_FAILED);
        }
    }

    Print("Grid Trend Multiplier EA initialized");
    Print("Direction Mode: ", EnumToString(DirectionMode));
    Print("Initial Balance: ", InitialBalance);

    // Initial bias calculation for AUTO mode
    if (DirectionMode == AUTO)
    {
        RecalcDailyBias();
    }

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    if (h_ema200_daily != INVALID_HANDLE)
        IndicatorRelease(h_ema200_daily);
    if (h_adx_h4 != INVALID_HANDLE)
        IndicatorRelease(h_adx_h4);

    Print("Grid Trend Multiplier EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check for daily reset
    CheckDailyReset();

    // If paused for the day, do nothing
    if (g_pausedForDay)
        return;

    // Check trading session
    if (!IsInTradingSession())
        return;

    // Update current grid count
    UpdateGridCount();

    // Check for TP hits and close profitable positions
    CheckAndClosePositions();

    // Handle EXIT-ONLY mode
    if (g_exitMode)
    {
        HandleExitMode();
        return;
    }

    // Get current trading direction
    ENUM_DIRECTION currentDirection = GetCurrentDirection();
    if (currentDirection == -1) // No valid direction
        return;

    // If no positions exist, open seed trade
    if (CurrentGridCount == 0)
    {
        OpenSeedTrade(currentDirection);
        return;
    }

    // Check for pullback and add new grid leg if conditions are met
    if (CurrentGridCount < MaxGrids)
    {
        CheckAndAddGridLeg(currentDirection);
    }
}

//+------------------------------------------------------------------+
//| Check for daily reset and recalculate bias                      |
//+------------------------------------------------------------------+
void CheckDailyReset()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    if (dt.hour == TargetResetHour && g_lastResetTime < TimeCurrent() - 3600)
    {
        g_lastResetTime = TimeCurrent();
        g_todayProfitClosed = 0.0;
        g_exitMode = false;
        g_pausedForDay = false;
        g_anchorTicket = 0;

        Print("Daily reset at ", TimeToString(TimeCurrent()));

        // Close all positions if CloseAllEOD is enabled
        if (CloseAllEOD)
        {
            CloseAllPositions();
        }

        // Recalculate daily bias for AUTO mode
        if (DirectionMode == AUTO)
        {
            RecalcDailyBias();
        }
    }
}

//+------------------------------------------------------------------+
//| Get current trading direction based on mode                     |
//+------------------------------------------------------------------+
ENUM_DIRECTION GetCurrentDirection()
{
    if (DirectionMode == MANUAL_BUY)
        return DIR_BUY;
    else if (DirectionMode == MANUAL_SELL)
        return DIR_SELL;
    else if (DirectionMode == AUTO)
    {
        if (g_dayBias == 1)
            return DIR_BUY;
        else if (g_dayBias == -1)
            return DIR_SELL;
        else
            return (ENUM_DIRECTION)-1; // No valid direction
    }

    return (ENUM_DIRECTION)-1;
}

//+------------------------------------------------------------------+
//| Recalculate daily bias using higher timeframe indicators        |
//+------------------------------------------------------------------+
void RecalcDailyBias()
{
    if (DirectionMode != AUTO)
        return;

    // Get Daily EMA-200 data
    double ema200[2];
    double close_price = iClose(Symbol(), PERIOD_D1, 0);

    if (CopyBuffer(h_ema200_daily, 0, 0, 2, ema200) != 2)
    {
        Print("Failed to get EMA-200 data");
        g_dayBias = 0;
        return;
    }

    // Calculate EMA slope
    double ema_slope = ema200[0] - ema200[1];

    // Get H4 ADX data
    double adx_value[1];
    if (CopyBuffer(h_adx_h4, 0, 0, 1, adx_value) != 1)
    {
        Print("Failed to get ADX data");
        g_dayBias = 0;
        return;
    }

    // Determine bias
    if (adx_value[0] > ADX_Threshold)
    {
        if (ema_slope > 0 && close_price > ema200[0])
        {
            g_dayBias = 1; // Bullish
            Print("Daily bias: BULLISH (EMA slope: ", DoubleToString(ema_slope, 5), ", ADX: ", DoubleToString(adx_value[0], 2), ")");
        }
        else if (ema_slope < 0 && close_price < ema200[0])
        {
            g_dayBias = -1; // Bearish
            Print("Daily bias: BEARISH (EMA slope: ", DoubleToString(ema_slope, 5), ", ADX: ", DoubleToString(adx_value[0], 2), ")");
        }
        else
        {
            g_dayBias = 0; // No trend
            Print("Daily bias: NO TREND (EMA slope: ", DoubleToString(ema_slope, 5), ", ADX: ", DoubleToString(adx_value[0], 2), ")");
        }
    }
    else
    {
        g_dayBias = 0; // Weak trend
        Print("Daily bias: WEAK TREND (ADX: ", DoubleToString(adx_value[0], 2), " < ", ADX_Threshold, ")");
    }
}

//+------------------------------------------------------------------+
//| Check if current time is within trading session                 |
//+------------------------------------------------------------------+
bool IsInTradingSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;
    
    if (SessionStartHour <= SessionEndHour)
    {
        // Normal session (e.g., 8 to 17)
        return (currentHour >= SessionStartHour && currentHour <= SessionEndHour);
    }
    else
    {
        // Overnight session (e.g., 22 to 6)
        return (currentHour >= SessionStartHour || currentHour <= SessionEndHour);
    }
}

//+------------------------------------------------------------------+
//| Handle EXIT-ONLY mode                                           |
//+------------------------------------------------------------------+
void HandleExitMode()
{
    // Find and track the anchor ticket (oldest position of today)
    if (g_anchorTicket == 0)
    {
        FindAnchorTicket();
    }

    // Check if anchor ticket still exists and its profit
    if (g_anchorTicket > 0)
    {
        if (PositionSelectByTicket(g_anchorTicket))
        {
            double anchorProfit = PositionGetDouble(POSITION_PROFIT);
            if (anchorProfit >= 0)
            {
                // Close anchor ticket
                if (ClosePositionByTicket(g_anchorTicket))
                {
                    Print("Anchor ticket closed with profit: ", DoubleToString(anchorProfit, 2));
                    g_anchorTicket = 0;

                    // Close any remaining orphan positions
                    CloseAllPositions();

                    // Pause for the day
                    g_pausedForDay = true;
                    Print("EXIT-ONLY mode completed. EA paused for the day.");
                }
            }
        }
        else
        {
            // Anchor ticket no longer exists, close remaining positions
            g_anchorTicket = 0;
            CloseAllPositions();
            g_pausedForDay = true;
            Print("Anchor ticket closed externally. EA paused for the day.");
        }
    }
    else
    {
        // No anchor ticket found, close all and pause
        CloseAllPositions();
        g_pausedForDay = true;
        Print("No anchor ticket found. EA paused for the day.");
    }
}

//+------------------------------------------------------------------+
//| Find the oldest ticket of today                                 |
//+------------------------------------------------------------------+
void FindAnchorTicket()
{
    datetime oldestTime = TimeCurrent();
    g_anchorTicket = 0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                datetime posTime = (datetime)PositionGetInteger(POSITION_TIME);
                if (posTime < oldestTime)
                {
                    oldestTime = posTime;
                    g_anchorTicket = PositionGetInteger(POSITION_TICKET);
                }
            }
        }
    }

    if (g_anchorTicket > 0)
    {
        Print("Anchor ticket identified: ", g_anchorTicket, " opened at ", TimeToString(oldestTime));
    }
}

//+------------------------------------------------------------------+
//| Update current grid count                                        |
//+------------------------------------------------------------------+
void UpdateGridCount()
{
    CurrentGridCount = 0;
    
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                CurrentGridCount++;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check and close positions that hit TP                           |
//+------------------------------------------------------------------+
void CheckAndClosePositions()
{
    double askPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double bidPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double pipValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if (SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
        pipValue *= 10;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                double lots = PositionGetDouble(POSITION_VOLUME);
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

                bool shouldClose = false;

                if (posType == POSITION_TYPE_BUY)
                {
                    if (bidPrice >= openPrice + (GridSpacePips * pipValue))
                        shouldClose = true;
                }
                else if (posType == POSITION_TYPE_SELL)
                {
                    if (askPrice <= openPrice - (GridSpacePips * pipValue))
                        shouldClose = true;
                }
                
                if (shouldClose)
                {
                    ulong ticket = PositionGetInteger(POSITION_TICKET);
                    double positionProfit = PositionGetDouble(POSITION_PROFIT);

                    if (ClosePosition(ticket))
                    {
                        Print("Position closed at TP. Ticket: ", ticket, " Profit: ", DoubleToString(positionProfit, 2));

                        // Track daily profit
                        g_todayProfitClosed += positionProfit;

                        // Check if daily target reached
                        if (g_todayProfitClosed >= DailyTargetUSD && !g_exitMode)
                        {
                            g_exitMode = true;
                            Print("Daily target reached! Profit: $", DoubleToString(g_todayProfitClosed, 2), " - Entering EXIT-ONLY mode");
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open seed trade                                                  |
//+------------------------------------------------------------------+
void OpenSeedTrade(ENUM_DIRECTION direction)
{
    if (OpenMarketOrder(direction, LotSize))
    {
        Print("Seed trade opened. Direction: ", (direction == DIR_BUY) ? "BUY" : "SELL");
        UpdateReferencePrice();
    }
}

//+------------------------------------------------------------------+
//| Check and add grid leg on pullback                              |
//+------------------------------------------------------------------+
void CheckAndAddGridLeg(ENUM_DIRECTION direction)
{
    UpdateReferencePrice();

    if (ReferencePrice == 0.0)
        return;

    double currentPrice = (direction == DIR_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double pipValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if (SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
        pipValue *= 10;

    bool shouldAddLeg = false;

    if (direction == DIR_BUY)
    {
        // For BUY: add leg when price retreats (goes down) from reference
        if (currentPrice <= ReferencePrice - (GridSpacePips * pipValue))
            shouldAddLeg = true;
    }
    else
    {
        // For SELL: add leg when price retreats (goes up) from reference
        if (currentPrice >= ReferencePrice + (GridSpacePips * pipValue))
            shouldAddLeg = true;
    }

    if (shouldAddLeg)
    {
        if (OpenMarketOrder(direction, LotSize))
        {
            Print("Grid leg added. Current grid count: ", CurrentGridCount + 1);
            UpdateReferencePrice();
        }
    }
}

//+------------------------------------------------------------------+
//| Update reference price                                           |
//+------------------------------------------------------------------+
void UpdateReferencePrice()
{
    ReferencePrice = 0.0;
    bool firstPosition = true;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);

                if (firstPosition)
                {
                    ReferencePrice = openPrice;
                    firstPosition = false;
                }
                else
                {
                    if (Direction == DIR_BUY)
                    {
                        // For BUY: reference is lowest open price
                        if (openPrice < ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                    else
                    {
                        // For SELL: reference is highest open price
                        if (openPrice > ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open market order                                                |
//+------------------------------------------------------------------+
bool OpenMarketOrder(ENUM_DIRECTION dir, double lots)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lots;
    request.type = (dir == DIR_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    request.price = (dir == DIR_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    request.deviation = 10;
    request.magic = Magic;
    request.comment = "GridTrendMultiplier";

    if (!OrderSend(request, result))
    {
        Print("OrderSend failed. Error: ", GetLastError());
        return false;
    }

    if (result.retcode == TRADE_RETCODE_DONE)
    {
        Print("Order opened successfully. Ticket: ", result.order);
        return true;
    }
    else
    {
        Print("OrderSend failed. Return code: ", result.retcode);
        return false;
    }
}

//+------------------------------------------------------------------+
//| Close position by ticket                                         |
//+------------------------------------------------------------------+
bool ClosePosition(ulong ticket)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    if (!PositionSelectByTicket(ticket))
        return false;

    request.action = TRADE_ACTION_DEAL;
    request.symbol = PositionGetString(POSITION_SYMBOL);
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
    request.position = ticket;
    request.price = (request.type == ORDER_TYPE_SELL) ? SymbolInfoDouble(request.symbol, SYMBOL_BID) : SymbolInfoDouble(request.symbol, SYMBOL_ASK);
    request.deviation = 10;
    request.magic = Magic;

    if (!OrderSend(request, result))
    {
        Print("Close order failed. Error: ", GetLastError());
        return false;
    }

    if (result.retcode == TRADE_RETCODE_DONE)
    {
        return true;
    }
    else
    {
        Print("Close order failed. Return code: ", result.retcode);
        return false;
    }
}

//+------------------------------------------------------------------+
//| Close position by ticket                                         |
//+------------------------------------------------------------------+
bool ClosePositionByTicket(ulong ticket)
{
    return ClosePosition(ticket);
}

//+------------------------------------------------------------------+
//| Close all positions                                              |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                ulong ticket = PositionGetInteger(POSITION_TICKET);
                ClosePosition(ticket);
            }
        }
    }
    Print("All positions closed");
}
