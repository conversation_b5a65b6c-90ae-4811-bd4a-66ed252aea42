//+------------------------------------------------------------------+
//|                                           GridTrendMultiplier.mq5 |
//|                                Advanced Grid Scalper with AUTO   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "2.00"

//--- Enums
enum ENUM_DIRECTION { DIR_BUY = 0, DIR_SELL = 1 };
enum ENUM_DIRMODE { MANUAL_BUY = 0, MANUAL_SELL = 1, AUTO_MODE = 2 };

//--- Account & Risk
input double          MaxDrawdownPct     = 20.0;        // % of balance willing to float today

//--- Leg Sizing
input double          ATR_Multiplier     = 3.5;         // ATR multiplier for leg size
input int             MaxLegsCap         = 12;          // Hard ceiling for legs

//--- Trend Filter
input ENUM_DIRMODE    DirectionMode      = AUTO_MODE;   // BUY/SELL/AUTO
input int             ADX_Threshold      = 20;          // Minimum H4 ADX for trend
input int             VWAP_ConfirmBars   = 4;           // M15 bars to confirm VWAP bias

//--- Daily Performance Stop
input double          DailyTargetPct     = 5.0;         // Daily profit target %
input int             TargetResetHour    = 0;           // Daily reset hour

//--- Operations
input int             SpreadMaxPips      = 5;           // Max spread for new entries
input bool            CloseAllEOD        = false;       // Flatten at EOD
input int             SessionStartHour   = 0;           // Trading window start
input int             SessionEndHour     = 23;          // Trading window end
input uint            Magic              = 555555;      // Magic number

//--- Global variables
double InitialBalance = 0.0;
double ReferencePrice = 0.0;
int    CurrentGridCount = 0;

//--- Dynamic parameters (calculated daily)
int    g_legSizePips = 0;          // Today's leg size from ATR
double g_lotSize = 0.0;            // Today's calculated lot size
int    g_maxLegs = 0;              // Today's feasible leg count
double g_pipValueLocal = 0.0;      // Pip value in risk currency

//--- Daily bias and profit tracking
int    g_dayBias = 0;              // -1=SELL, +1=BUY, 0=FLAT
double g_todayProfitClosed = 0.0;  // Sum of closed profits today
bool   g_exitMode = false;         // EXIT-ONLY state
bool   g_pausedForDay = false;     // Paused until next reset
ulong  g_anchorTicket = 0;         // Oldest ticket of today
datetime g_lastResetTime = 0;      // Last reset timestamp
double g_dailyTargetLocal = 0.0;   // Daily target in account currency
double g_maxDrawdownLocal = 0.0;   // Max DD in account currency

//--- VWAP tracking
double g_vwapAnchor = 0.0;         // Anchored VWAP value
int    g_vwapConfirmCount = 0;     // Consecutive bars confirming bias
datetime g_vwapAnchorTime = 0;     // When VWAP was anchored

//--- Indicator handles
int    h_atr_m15 = INVALID_HANDLE;
int    h_ema200_daily = INVALID_HANDLE;
int    h_adx_h4 = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    InitialBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    CurrentGridCount = 0;
    ReferencePrice = 0.0;
    g_dayBias = 0;
    g_todayProfitClosed = 0.0;
    g_exitMode = false;
    g_pausedForDay = false;
    g_anchorTicket = 0;
    g_lastResetTime = 0;
    g_vwapAnchor = 0.0;
    g_vwapConfirmCount = 0;
    g_vwapAnchorTime = 0;

    // Initialize indicators
    h_atr_m15 = iATR(Symbol(), PERIOD_M15, 14);
    h_ema200_daily = iMA(Symbol(), PERIOD_D1, 200, 0, MODE_EMA, PRICE_CLOSE);
    h_adx_h4 = iADX(Symbol(), PERIOD_H4, 14);

    if (h_atr_m15 == INVALID_HANDLE || h_ema200_daily == INVALID_HANDLE || h_adx_h4 == INVALID_HANDLE)
    {
        Print("Failed to initialize indicators");
        return(INIT_FAILED);
    }

    Print("Grid Trend Multiplier EA v2.0 initialized");
    Print("Direction Mode: ", EnumToString(DirectionMode));
    Print("Max Drawdown: ", MaxDrawdownPct, "%");
    Print("ATR Multiplier: ", ATR_Multiplier);
    Print("Daily Target: ", DailyTargetPct, "%");

    // Initial parameter calculation
    CalculateDailyParameters();

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    if (h_atr_m15 != INVALID_HANDLE)
        IndicatorRelease(h_atr_m15);
    if (h_ema200_daily != INVALID_HANDLE)
        IndicatorRelease(h_ema200_daily);
    if (h_adx_h4 != INVALID_HANDLE)
        IndicatorRelease(h_adx_h4);

    Print("Grid Trend Multiplier EA v2.0 deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check for daily reset
    CheckDailyReset();

    // If paused for the day, do nothing
    if (g_pausedForDay)
        return;

    // Check if we have valid parameters
    if (g_maxLegs == 0 || g_lotSize == 0.0)
        return;

    // Check trading session
    if (!IsInTradingSession())
        return;

    // Check VWAP confirmation for AUTO mode
    if (DirectionMode == AUTO_MODE)
        CheckVWAPConfirmation();

    // Update current grid count
    UpdateGridCount();

    // Check for TP hits and close profitable positions
    CheckAndClosePositions();

    // Check drawdown guard
    if (CheckDrawdownGuard())
        return;

    // Handle EXIT-ONLY mode
    if (g_exitMode)
    {
        HandleExitMode();
        return;
    }

    // Get current trading direction
    int currentDirection = GetCurrentDirection();
    if (currentDirection == -1) // No valid direction
        return;

    // Check spread before new entries
    if (!IsSpreadAcceptable())
        return;

    // If no positions exist, open seed trade
    if (CurrentGridCount == 0)
    {
        OpenSeedTrade((ENUM_DIRECTION)currentDirection);
        return;
    }

    // Check for pullback and add new grid leg if conditions are met
    if (CurrentGridCount < g_maxLegs)
    {
        CheckAndAddGridLeg((ENUM_DIRECTION)currentDirection);
    }
}

//+------------------------------------------------------------------+
//| Calculate daily parameters (leg size, lot size, max legs)       |
//+------------------------------------------------------------------+
void CalculateDailyParameters()
{
    // Get current balance
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);

    // Calculate pip value in account currency
    CalculatePipValue();

    // Get M15 ATR-14 for leg sizing
    double atr_values[1];
    if (CopyBuffer(h_atr_m15, 0, 0, 1, atr_values) != 1)
    {
        Print("Failed to get ATR data, using default leg size");
        g_legSizePips = 20;
    }
    else
    {
        double atr_points = atr_values[0];
        double atr_pips = atr_points;

        // Convert points to pips for 5/3 digit brokers
        if (SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
            atr_pips = atr_points / 10.0;

        g_legSizePips = (int)MathRound(atr_pips * ATR_Multiplier);
        if (g_legSizePips < 5) g_legSizePips = 5; // Minimum leg size
    }

    // Calculate max drawdown in account currency
    g_maxDrawdownLocal = balance * (MaxDrawdownPct / 100.0);

    // Calculate daily target in account currency (percentage only)
    g_dailyTargetLocal = balance * (DailyTargetPct / 100.0);

    // Solve for feasible grid depth
    SolveFeasibleGrid();

    Print("Daily Parameters Calculated:");
    Print("- Leg Size: ", g_legSizePips, " pips");
    Print("- Max Legs: ", g_maxLegs);
    Print("- Lot Size: ", DoubleToString(g_lotSize, 2));
    Print("- Daily Target: ", DoubleToString(g_dailyTargetLocal, 2), " ", AccountInfoString(ACCOUNT_CURRENCY));
    Print("- Max Drawdown: ", DoubleToString(g_maxDrawdownLocal, 2), " ", AccountInfoString(ACCOUNT_CURRENCY));
}

//+------------------------------------------------------------------+
//| Calculate pip value in account currency                         |
//+------------------------------------------------------------------+
void CalculatePipValue()
{
    double pipSize = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if (SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
        pipSize *= 10;

    // Get pip value in account currency
    g_pipValueLocal = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE) * pipSize / SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
}

//+------------------------------------------------------------------+
//| Solve for feasible grid depth within drawdown limits           |
//+------------------------------------------------------------------+
void SolveFeasibleGrid()
{
    g_maxLegs = MaxLegsCap;

    // Triangular loss formula: MaxLoss = pipValue × legSize × (N-1)×N/2
    while (g_maxLegs >= 2)
    {
        double triangularLoss = g_pipValueLocal * g_legSizePips * (g_maxLegs - 1) * g_maxLegs / 2.0;

        if (triangularLoss <= g_maxDrawdownLocal)
        {
            // Calculate lot size for this configuration
            g_lotSize = g_maxDrawdownLocal / triangularLoss;

            // Round down to broker step
            double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
            g_lotSize = MathFloor(g_lotSize / lotStep) * lotStep;

            // Check minimum lot size
            double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
            if (g_lotSize >= minLot)
            {
                break; // Found feasible configuration
            }
        }

        g_maxLegs--;
    }

    if (g_maxLegs < 2)
    {
        Print("Cannot find feasible grid configuration - EA will stay flat today");
        g_lotSize = 0.0;
        g_maxLegs = 0;
    }
}

//+------------------------------------------------------------------+
//| Check for daily reset and recalculate bias                      |
//+------------------------------------------------------------------+
void CheckDailyReset()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    if (dt.hour == TargetResetHour && g_lastResetTime < TimeCurrent() - 3600)
    {
        g_lastResetTime = TimeCurrent();
        g_todayProfitClosed = 0.0;
        g_exitMode = false;
        g_pausedForDay = false;
        g_anchorTicket = 0;
        g_vwapAnchor = 0.0;
        g_vwapConfirmCount = 0;
        g_vwapAnchorTime = 0;

        Print("=== DAILY RESET at ", TimeToString(TimeCurrent()), " ===");

        // Close all positions if CloseAllEOD is enabled
        if (CloseAllEOD)
        {
            CloseAllPositions();
        }

        // Recalculate daily parameters
        CalculateDailyParameters();

        // Recalculate daily bias for AUTO mode
        if (DirectionMode == AUTO_MODE)
        {
            RecalcDailyBias();
        }

        Print("=== RESET COMPLETE ===");
    }
}

//+------------------------------------------------------------------+
//| Check if spread is acceptable for new entries                   |
//+------------------------------------------------------------------+
bool IsSpreadAcceptable()
{
    long spread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);
    double spreadPips = (double)spread;

    // Convert to pips for 5/3 digit brokers
    if (SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
        spreadPips = spread / 10.0;

    return (spreadPips <= SpreadMaxPips);
}

//+------------------------------------------------------------------+
//| Check drawdown guard                                            |
//+------------------------------------------------------------------+
bool CheckDrawdownGuard()
{
    double floatingLoss = 0.0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                double profit = PositionGetDouble(POSITION_PROFIT);
                if (profit < 0)
                    floatingLoss += MathAbs(profit);
            }
        }
    }

    if (floatingLoss >= g_maxDrawdownLocal)
    {
        Print("DRAWDOWN GUARD TRIGGERED! Floating loss: ", DoubleToString(floatingLoss, 2), " >= ", DoubleToString(g_maxDrawdownLocal, 2));
        CloseAllPositions();
        g_pausedForDay = true;
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| Get current trading direction based on mode                     |
//+------------------------------------------------------------------+
int GetCurrentDirection()
{
    if (DirectionMode == MANUAL_BUY)
        return DIR_BUY;
    else if (DirectionMode == MANUAL_SELL)
        return DIR_SELL;
    else if (DirectionMode == AUTO_MODE)
    {
        // Check VWAP confirmation
        if (g_vwapConfirmCount < VWAP_ConfirmBars)
            return -1; // Wait for confirmation

        if (g_dayBias == 1)
            return DIR_BUY;
        else if (g_dayBias == -1)
            return DIR_SELL;
        else
            return -1; // No valid direction
    }

    return -1;
}

//+------------------------------------------------------------------+
//| Recalculate daily bias using higher timeframe indicators        |
//+------------------------------------------------------------------+
void RecalcDailyBias()
{
    if (DirectionMode != AUTO_MODE)
        return;

    // Step 1: Macro filter - Daily EMA-200 slope + H4 ADX
    int macroFilter = GetMacroFilter();

    if (macroFilter == 0)
    {
        g_dayBias = 0;
        Print("Macro filter: NO TREND - EA will stay flat today");
        return;
    }

    // Step 2: Intraday confirmation - Anchored VWAP
    // Reset VWAP anchor for new day
    g_vwapAnchor = CalculateVWAP();
    g_vwapAnchorTime = TimeCurrent();
    g_vwapConfirmCount = 0;

    Print("Macro filter suggests: ", (macroFilter == 1) ? "BULLISH" : "BEARISH");
    Print("Waiting for VWAP confirmation...");

    // Initial bias set to macro filter, but will be confirmed by VWAP
    g_dayBias = macroFilter;
}

//+------------------------------------------------------------------+
//| Get macro trend filter result                                   |
//+------------------------------------------------------------------+
int GetMacroFilter()
{
    // Get Daily EMA-200 data
    double ema200[2];
    double close_price = iClose(Symbol(), PERIOD_D1, 0);

    if (CopyBuffer(h_ema200_daily, 0, 0, 2, ema200) != 2)
    {
        Print("Failed to get EMA-200 data");
        return 0;
    }

    // Calculate EMA slope
    double ema_slope = ema200[0] - ema200[1];

    // Get H4 ADX data
    double adx_value[1];
    if (CopyBuffer(h_adx_h4, 0, 0, 1, adx_value) != 1)
    {
        Print("Failed to get ADX data");
        return 0;
    }

    // Check ADX threshold
    if (adx_value[0] <= ADX_Threshold)
    {
        Print("ADX too weak: ", DoubleToString(adx_value[0], 2), " <= ", ADX_Threshold);
        return 0;
    }

    // Determine macro bias
    if (ema_slope > 0 && close_price > ema200[0])
    {
        Print("Macro: BULLISH (EMA slope: ", DoubleToString(ema_slope, 5), ", Price > EMA, ADX: ", DoubleToString(adx_value[0], 2), ")");
        return 1;
    }
    else if (ema_slope < 0 && close_price < ema200[0])
    {
        Print("Macro: BEARISH (EMA slope: ", DoubleToString(ema_slope, 5), ", Price < EMA, ADX: ", DoubleToString(adx_value[0], 2), ")");
        return -1;
    }
    else
    {
        Print("Macro: MIXED SIGNALS (EMA slope: ", DoubleToString(ema_slope, 5), ", ADX: ", DoubleToString(adx_value[0], 2), ")");
        return 0;
    }
}

//+------------------------------------------------------------------+
//| Calculate anchored VWAP                                         |
//+------------------------------------------------------------------+
double CalculateVWAP()
{
    // Simple VWAP calculation for current M15 bar
    double high = iHigh(Symbol(), PERIOD_M15, 0);
    double low = iLow(Symbol(), PERIOD_M15, 0);
    double close = iClose(Symbol(), PERIOD_M15, 0);
    long volume = iTickVolume(Symbol(), PERIOD_M15, 0);

    if (volume <= 0) volume = 1; // Fallback for non-volume symbols

    double typical_price = (high + low + close) / 3.0;
    return typical_price; // Simplified VWAP anchor
}

//+------------------------------------------------------------------+
//| Check VWAP confirmation                                         |
//+------------------------------------------------------------------+
void CheckVWAPConfirmation()
{
    if (DirectionMode != AUTO_MODE || g_dayBias == 0)
        return;

    double currentPrice = iClose(Symbol(), PERIOD_M15, 0);
    double currentVWAP = CalculateVWAP();

    bool confirmsBias = false;

    if (g_dayBias == 1) // Bullish bias
    {
        if (currentPrice >= g_vwapAnchor && (currentVWAP - g_vwapAnchor) > 0)
            confirmsBias = true;
    }
    else if (g_dayBias == -1) // Bearish bias
    {
        if (currentPrice <= g_vwapAnchor && (currentVWAP - g_vwapAnchor) < 0)
            confirmsBias = true;
    }

    if (confirmsBias)
    {
        g_vwapConfirmCount++;
        if (g_vwapConfirmCount >= VWAP_ConfirmBars)
        {
            Print("VWAP confirmation complete! Bias confirmed: ", (g_dayBias == 1) ? "BULLISH" : "BEARISH");
        }
    }
    else
    {
        g_vwapConfirmCount = 0; // Reset counter
    }
}

//+------------------------------------------------------------------+
//| Check if current time is within trading session                 |
//+------------------------------------------------------------------+
bool IsInTradingSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;
    
    if (SessionStartHour <= SessionEndHour)
    {
        // Normal session (e.g., 8 to 17)
        return (currentHour >= SessionStartHour && currentHour <= SessionEndHour);
    }
    else
    {
        // Overnight session (e.g., 22 to 6)
        return (currentHour >= SessionStartHour || currentHour <= SessionEndHour);
    }
}

//+------------------------------------------------------------------+
//| Handle EXIT-ONLY mode                                           |
//+------------------------------------------------------------------+
void HandleExitMode()
{
    // Find and track the anchor ticket (oldest position of today)
    if (g_anchorTicket == 0)
    {
        FindAnchorTicket();
    }

    // Check if anchor ticket still exists and its profit
    if (g_anchorTicket > 0)
    {
        if (PositionSelectByTicket(g_anchorTicket))
        {
            double anchorProfit = PositionGetDouble(POSITION_PROFIT);
            if (anchorProfit >= 0)
            {
                // Close anchor ticket
                if (ClosePositionByTicket(g_anchorTicket))
                {
                    Print("Anchor ticket closed with profit: ", DoubleToString(anchorProfit, 2));
                    g_anchorTicket = 0;

                    // Close any remaining orphan positions
                    CloseAllPositions();

                    // Pause for the day
                    g_pausedForDay = true;
                    Print("EXIT-ONLY mode completed. EA paused for the day.");
                }
            }
        }
        else
        {
            // Anchor ticket no longer exists, close remaining positions
            g_anchorTicket = 0;
            CloseAllPositions();
            g_pausedForDay = true;
            Print("Anchor ticket closed externally. EA paused for the day.");
        }
    }
    else
    {
        // No anchor ticket found, close all and pause
        CloseAllPositions();
        g_pausedForDay = true;
        Print("No anchor ticket found. EA paused for the day.");
    }
}

//+------------------------------------------------------------------+
//| Find the oldest ticket of today                                 |
//+------------------------------------------------------------------+
void FindAnchorTicket()
{
    datetime oldestTime = TimeCurrent();
    g_anchorTicket = 0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                datetime posTime = (datetime)PositionGetInteger(POSITION_TIME);
                if (posTime < oldestTime)
                {
                    oldestTime = posTime;
                    g_anchorTicket = PositionGetInteger(POSITION_TICKET);
                }
            }
        }
    }

    if (g_anchorTicket > 0)
    {
        Print("Anchor ticket identified: ", g_anchorTicket, " opened at ", TimeToString(oldestTime));
    }
}

//+------------------------------------------------------------------+
//| Update current grid count                                        |
//+------------------------------------------------------------------+
void UpdateGridCount()
{
    CurrentGridCount = 0;
    
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                CurrentGridCount++;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check and close positions that hit TP                           |
//+------------------------------------------------------------------+
void CheckAndClosePositions()
{
    double askPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double bidPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double pipValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if (SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
        pipValue *= 10;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                double lots = PositionGetDouble(POSITION_VOLUME);
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

                bool shouldClose = false;

                if (posType == POSITION_TYPE_BUY)
                {
                    if (bidPrice >= openPrice + (g_legSizePips * pipValue))
                        shouldClose = true;
                }
                else if (posType == POSITION_TYPE_SELL)
                {
                    if (askPrice <= openPrice - (g_legSizePips * pipValue))
                        shouldClose = true;
                }
                
                if (shouldClose)
                {
                    ulong ticket = PositionGetInteger(POSITION_TICKET);
                    double positionProfit = PositionGetDouble(POSITION_PROFIT);

                    if (ClosePosition(ticket))
                    {
                        Print("Position closed at TP. Ticket: ", ticket, " Profit: ", DoubleToString(positionProfit, 2));

                        // Track daily profit
                        g_todayProfitClosed += positionProfit;

                        // Check if daily target reached
                        if (g_todayProfitClosed >= g_dailyTargetLocal && !g_exitMode)
                        {
                            g_exitMode = true;
                            Print("Daily target reached! Profit: ", DoubleToString(g_todayProfitClosed, 2), " - Entering EXIT-ONLY mode");
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open seed trade                                                  |
//+------------------------------------------------------------------+
void OpenSeedTrade(ENUM_DIRECTION direction)
{
    if (OpenMarketOrder(direction, g_lotSize))
    {
        Print("Seed trade opened. Direction: ", (direction == DIR_BUY) ? "BUY" : "SELL", " Lot: ", DoubleToString(g_lotSize, 2));
        UpdateReferencePrice();
    }
}

//+------------------------------------------------------------------+
//| Check and add grid leg on pullback                              |
//+------------------------------------------------------------------+
void CheckAndAddGridLeg(ENUM_DIRECTION direction)
{
    UpdateReferencePrice();

    if (ReferencePrice == 0.0)
        return;

    double currentPrice = (direction == DIR_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double pipValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if (SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 5 || SymbolInfoInteger(Symbol(), SYMBOL_DIGITS) == 3)
        pipValue *= 10;

    bool shouldAddLeg = false;

    if (direction == DIR_BUY)
    {
        // For BUY: add leg when price retreats (goes down) from reference
        if (currentPrice <= ReferencePrice - (g_legSizePips * pipValue))
            shouldAddLeg = true;
    }
    else
    {
        // For SELL: add leg when price retreats (goes up) from reference
        if (currentPrice >= ReferencePrice + (g_legSizePips * pipValue))
            shouldAddLeg = true;
    }

    if (shouldAddLeg)
    {
        if (OpenMarketOrder(direction, g_lotSize))
        {
            Print("Grid leg added. Count: ", CurrentGridCount + 1, "/", g_maxLegs, " Lot: ", DoubleToString(g_lotSize, 2));
            UpdateReferencePrice();
        }
    }
}

//+------------------------------------------------------------------+
//| Update reference price                                           |
//+------------------------------------------------------------------+
void UpdateReferencePrice()
{
    ReferencePrice = 0.0;
    bool firstPosition = true;
    ENUM_POSITION_TYPE positionType = POSITION_TYPE_BUY; // Default

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);

                if (firstPosition)
                {
                    ReferencePrice = openPrice;
                    positionType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                    firstPosition = false;
                }
                else
                {
                    if (positionType == POSITION_TYPE_BUY)
                    {
                        // For BUY: reference is lowest open price
                        if (openPrice < ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                    else
                    {
                        // For SELL: reference is highest open price
                        if (openPrice > ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open market order                                                |
//+------------------------------------------------------------------+
bool OpenMarketOrder(ENUM_DIRECTION dir, double lots)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lots;
    request.type = (dir == DIR_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    request.price = (dir == DIR_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    request.deviation = 10;
    request.magic = Magic;
    request.comment = "GridTrendMultiplier";

    if (!OrderSend(request, result))
    {
        Print("OrderSend failed. Error: ", GetLastError());
        return false;
    }

    if (result.retcode == TRADE_RETCODE_DONE)
    {
        Print("Order opened successfully. Ticket: ", result.order);
        return true;
    }
    else
    {
        Print("OrderSend failed. Return code: ", result.retcode);
        return false;
    }
}

//+------------------------------------------------------------------+
//| Close position by ticket                                         |
//+------------------------------------------------------------------+
bool ClosePosition(ulong ticket)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    if (!PositionSelectByTicket(ticket))
        return false;

    request.action = TRADE_ACTION_DEAL;
    request.symbol = PositionGetString(POSITION_SYMBOL);
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
    request.position = ticket;
    request.price = (request.type == ORDER_TYPE_SELL) ? SymbolInfoDouble(request.symbol, SYMBOL_BID) : SymbolInfoDouble(request.symbol, SYMBOL_ASK);
    request.deviation = 10;
    request.magic = Magic;

    if (!OrderSend(request, result))
    {
        Print("Close order failed. Error: ", GetLastError());
        return false;
    }

    if (result.retcode == TRADE_RETCODE_DONE)
    {
        return true;
    }
    else
    {
        Print("Close order failed. Return code: ", result.retcode);
        return false;
    }
}

//+------------------------------------------------------------------+
//| Close position by ticket                                         |
//+------------------------------------------------------------------+
bool ClosePositionByTicket(ulong ticket)
{
    return ClosePosition(ticket);
}

//+------------------------------------------------------------------+
//| Close all positions                                              |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                ulong ticket = PositionGetInteger(POSITION_TICKET);
                ClosePosition(ticket);
            }
        }
    }
    Print("All positions closed");
}
