//+------------------------------------------------------------------+
//| GridTrendMultiplier.mq5                                          |
//| Copyright 2024, MetaQuotes Software Corp.                       |
//| https://www.mql5.com                                             |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input double GridMultiplier = 1.5;     // Grid spacing multiplier
input double LotMultiplier = 1.5;      // Lot size multiplier per leg
input double BaseLotSize = 0.01;       // Starting lot size
input int Magic = 12345;               // Magic number
input string TradeComment = "GTM";     // Trade comment

//--- Global variables
double ReferencePrice = 0.0;           // Reference price for grid calculation
int CurrentLegCount = 0;               // Current number of legs
int Direction = 0;                     // 0 = Buy, 1 = Sell

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("GridTrendMultiplier EA initialized");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("GridTrendMultiplier EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Update current leg count and reference price
    UpdateGridInfo();

    // Check for new grid level
    if (ShouldOpenNewLeg())
    {
        OpenNewLeg();
    }

    // Check for profit target
    if (ShouldCloseAll())
    {
        CloseAllPositions();
    }
}

//+------------------------------------------------------------------+
//| Update grid information                                          |
//+------------------------------------------------------------------+
void UpdateGridInfo()
{
    CurrentLegCount = 0;
    ReferencePrice = 0.0;
    bool firstPosition = true;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                CurrentLegCount++;
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);

                if (firstPosition)
                {
                    ReferencePrice = openPrice;
                    Direction = (int)PositionGetInteger(POSITION_TYPE);
                    firstPosition = false;
                }
                else
                {
                    if (Direction == POSITION_TYPE_BUY)
                    {
                        if (openPrice < ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                    else
                    {
                        if (openPrice > ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check if should open new leg                                     |
//+------------------------------------------------------------------+
bool ShouldOpenNewLeg()
{
    if (CurrentLegCount == 0)
        return true; // First position

    double currentPrice = (Direction == POSITION_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double gridSize = CalculateGridSize();

    if (Direction == POSITION_TYPE_BUY)
    {
        return (ReferencePrice - currentPrice) >= gridSize;
    }
    else
    {
        return (currentPrice - ReferencePrice) >= gridSize;
    }
}

//+------------------------------------------------------------------+
//| Calculate grid size                                              |
//+------------------------------------------------------------------+
double CalculateGridSize()
{
    double atr = iATR(Symbol(), PERIOD_H1, 14, 1);
    return atr * GridMultiplier;
}

//+------------------------------------------------------------------+
//| Open new leg                                                     |
//+------------------------------------------------------------------+
void OpenNewLeg()
{
    double lotSize = BaseLotSize * MathPow(LotMultiplier, CurrentLegCount);

    if (CurrentLegCount == 0)
    {
        // First position - determine direction based on trend
        Direction = DetermineTrendDirection();
    }

    ENUM_ORDER_TYPE orderType = (Direction == POSITION_TYPE_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    double price = (Direction == POSITION_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lotSize;
    request.type = orderType;
    request.price = price;
    request.magic = Magic;
    request.comment = TradeComment;

    if (OrderSend(request, result))
    {
        Print("New leg opened: ", EnumToString(orderType), " ", lotSize, " lots at ", price);
    }
    else
    {
        Print("Failed to open new leg: ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Determine trend direction                                        |
//+------------------------------------------------------------------+
int DetermineTrendDirection()
{
    // Simple trend determination using moving averages
    double ma_fast = iMA(Symbol(), PERIOD_M15, 20, 0, MODE_SMA, PRICE_CLOSE, 1);
    double ma_slow = iMA(Symbol(), PERIOD_M15, 50, 0, MODE_SMA, PRICE_CLOSE, 1);

    return (ma_fast > ma_slow) ? POSITION_TYPE_BUY : POSITION_TYPE_SELL;
}

//+------------------------------------------------------------------+
//| Check if should close all positions                             |
//+------------------------------------------------------------------+
bool ShouldCloseAll()
{
    double totalProfit = 0.0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                totalProfit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }

    // Close if profit is positive
    return totalProfit > 0;
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                MqlTradeRequest request = {};
                MqlTradeResult result = {};

                request.action = TRADE_ACTION_DEAL;
                request.symbol = Symbol();
                request.volume = PositionGetDouble(POSITION_VOLUME);
                request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
                request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_BID) : SymbolInfoDouble(Symbol(), SYMBOL_ASK);
                request.magic = Magic;
                request.comment = TradeComment + "_Close";

                OrderSend(request, result);
            }
        }
    }

    Print("All positions closed");

    // Reset grid
    CurrentLegCount = 0;
    ReferencePrice = 0.0;
}

//+------------------------------------------------------------------+
//| Determine trend direction                                        |
//+------------------------------------------------------------------+
int DetermineTrendDirection()
{
    // Simple trend determination using moving averages
    double ma_fast = iMA(Symbol(), PERIOD_M15, 20, 0, MODE_SMA, PRICE_CLOSE, 1);
    double ma_slow = iMA(Symbol(), PERIOD_M15, 50, 0, MODE_SMA, PRICE_CLOSE, 1);

    return (ma_fast > ma_slow) ? POSITION_TYPE_BUY : POSITION_TYPE_SELL;
}

//+------------------------------------------------------------------+
//| Check if should close all positions                             |
//+------------------------------------------------------------------+
bool ShouldCloseAll()
{
    double totalProfit = 0.0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                totalProfit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }

    // Close if profit is positive
    return totalProfit > 0;
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                MqlTradeRequest request = {};
                MqlTradeResult result = {};

                request.action = TRADE_ACTION_DEAL;
                request.symbol = Symbol();
                request.volume = PositionGetDouble(POSITION_VOLUME);
                request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
                request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_BID) : SymbolInfoDouble(Symbol(), SYMBOL_ASK);
                request.magic = Magic;
                request.comment = TradeComment + "_Close";

                OrderSend(request, result);
            }
        }
    }

    Print("All positions closed");

    // Reset grid
    CurrentLegCount = 0;
    ReferencePrice = 0.0;
}