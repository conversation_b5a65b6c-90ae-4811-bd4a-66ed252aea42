//+------------------------------------------------------------------+
//| GridTrendMultiplier.mq5                                          |
//| Simple Grid EA - Opens grids in chosen direction                 |
//+------------------------------------------------------------------+
#property copyright "GridTrendMultiplier"
#property version   "1.00"

//--- Enums
enum ENUM_DIRECTION
{
    DIR_BUY = 0,     // BUY grids only
    DIR_SELL = 1     // SELL grids only
};

//--- Input parameters
input ENUM_DIRECTION Direction = DIR_BUY;    // Grid Direction
input double GridSizePips = 20.0;            // Grid size in pips
input double LotMultiplier = 1.5;            // Lot multiplier per grid
input double BaseLotSize = 0.01;             // Starting lot size
input int MaxGridCount = 10;                 // Maximum number of grids
input double ProfitTargetPct = 5.0;          // Profit target %
input int Magic = 12345;                     // Magic number
input string TradeComment = "GTM";           // Trade comment

//--- Global variables
double ReferencePrice = 0.0;                 // Reference price for grid
int CurrentGrids = 0;                        // Current number of grids
double GridSizePoints = 0.0;                 // Grid size in points
double InitialBalance = 0.0;                 // Starting balance

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Calculate grid size in points
    GridSizePoints = GridSizePips * Point;
    if (Digits == 5 || Digits == 3)
        GridSizePoints = GridSizePips * Point * 10;

    InitialBalance = AccountInfoDouble(ACCOUNT_BALANCE);

    Print("GridTrendMultiplier EA initialized");
    Print("Direction: ", EnumToString(Direction));
    Print("Grid Size: ", GridSizePips, " pips");
    Print("Max Grids: ", MaxGridCount);
    Print("Profit Target: ", ProfitTargetPct, "%");

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("GridTrendMultiplier EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    UpdateGridInfo();

    // Check profit target first
    if (CheckProfitTarget())
    {
        CloseAllPositions();
        return;
    }

    // Check if we should open new grid
    if (ShouldOpenNewGrid())
    {
        OpenNewGrid();
    }
}

//+------------------------------------------------------------------+
//| Update grid information                                          |
//+------------------------------------------------------------------+
void UpdateGridInfo()
{
    CurrentGrids = 0;
    ReferencePrice = 0.0;
    bool firstPosition = true;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                CurrentGrids++;
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);

                if (firstPosition)
                {
                    ReferencePrice = openPrice;
                    firstPosition = false;
                }
                else
                {
                    if (Direction == DIR_BUY)
                    {
                        if (openPrice < ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                    else
                    {
                        if (openPrice > ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check if should open new grid                                   |
//+------------------------------------------------------------------+
bool ShouldOpenNewGrid()
{
    if (CurrentGrids >= MaxGridCount)
        return false;

    if (CurrentGrids == 0)
        return true;

    double currentPrice = (Direction == DIR_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                         SymbolInfoDouble(Symbol(), SYMBOL_BID);

    if (Direction == DIR_BUY)
    {
        return (ReferencePrice - currentPrice) >= GridSizePoints;
    }
    else
    {
        return (currentPrice - ReferencePrice) >= GridSizePoints;
    }
}

//+------------------------------------------------------------------+
//| Open new grid position                                          |
//+------------------------------------------------------------------+
void OpenNewGrid()
{
    double lotSize = BaseLotSize * MathPow(LotMultiplier, CurrentGrids);

    ENUM_ORDER_TYPE orderType = (Direction == DIR_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    double price = (Direction == DIR_BUY) ?
                   SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                   SymbolInfoDouble(Symbol(), SYMBOL_BID);

    // Calculate TP = Grid Size
    double tp = 0.0;
    if (Direction == DIR_BUY)
        tp = price + GridSizePoints;
    else
        tp = price - GridSizePoints;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lotSize;
    request.type = orderType;
    request.price = price;
    request.tp = tp;
    request.magic = Magic;
    request.comment = TradeComment;

    if (OrderSend(request, result))
    {
        Print("Grid #", CurrentGrids + 1, " opened: ", EnumToString(orderType),
              " ", lotSize, " lots at ", price, " TP: ", tp);
    }
    else
    {
        Print("Failed to open grid: ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Check profit target                                             |
//+------------------------------------------------------------------+
bool CheckProfitTarget()
{
    double totalProfit = 0.0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                totalProfit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }

    double targetAmount = InitialBalance * (ProfitTargetPct / 100.0);

    if (totalProfit >= targetAmount)
    {
        Print("Profit target reached: $", DoubleToString(totalProfit, 2));
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    int closedCount = 0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                MqlTradeRequest request = {};
                MqlTradeResult result = {};

                request.action = TRADE_ACTION_DEAL;
                request.symbol = Symbol();
                request.volume = PositionGetDouble(POSITION_VOLUME);
                request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
                request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                               SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                               SymbolInfoDouble(Symbol(), SYMBOL_ASK);
                request.magic = Magic;
                request.comment = TradeComment + "_Close";

                if (OrderSend(request, result))
                    closedCount++;
            }
        }
    }

    Print("All ", closedCount, " positions closed - Profit target reached!");

    // Reset grid
    CurrentGrids = 0;
    ReferencePrice = 0.0;
}