//+------------------------------------------------------------------+
//| GridTrendMultiplier.mq5                                          |
//| Simple Grid EA with Manual Direction                             |
//+------------------------------------------------------------------+
#property copyright "GridTrendMultiplier"
#property version   "1.00"

//--- Enums
enum ENUM_TRADE_DIRECTION
{
    DIRECTION_BUY = 0,    // BUY only
    DIRECTION_SELL = 1    // SELL only
};

//--- Input parameters
input ENUM_TRADE_DIRECTION TradeDirection = DIRECTION_BUY;  // Trade Direction
input double GridSizePips = 20.0;          // Grid size in pips
input double LotMultiplier = 1.5;          // Lot multiplier per grid
input double BaseLotSize = 0.01;           // Starting lot size
input int MaxGrids = 10;                   // Maximum number of grids
input double ProfitTargetUSD = 50.0;       // Profit target in USD
input int Magic = 12345;                   // Magic number
input string TradeComment = "GTM";         // Trade comment

//--- Global variables
double ReferencePrice = 0.0;               // Reference price for grid
int CurrentGridCount = 0;                  // Current number of grids
double GridSizePoints = 0.0;               // Grid size in points

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Calculate grid size in points
    GridSizePoints = GridSizePips * Point;
    if (Digits == 5 || Digits == 3)
        GridSizePoints = GridSizePips * Point * 10;

    Print("GridTrendMultiplier EA initialized");
    Print("Direction: ", EnumToString(TradeDirection));
    Print("Grid Size: ", GridSizePips, " pips");
    Print("Max Grids: ", MaxGrids);
    Print("Profit Target: $", ProfitTargetUSD);

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("GridTrendMultiplier EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Update grid information
    UpdateGridInfo();

    // Check for profit target first
    if (CheckProfitTarget())
    {
        CloseAllPositions();
        return;
    }

    // Check if we should open a new grid
    if (ShouldOpenNewGrid())
    {
        OpenNewGrid();
    }
}

//+------------------------------------------------------------------+
//| Update grid information                                          |
//+------------------------------------------------------------------+
void UpdateGridInfo()
{
    CurrentGridCount = 0;
    ReferencePrice = 0.0;
    bool firstPosition = true;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                CurrentGridCount++;
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);

                if (firstPosition)
                {
                    ReferencePrice = openPrice;
                    firstPosition = false;
                }
                else
                {
                    // Update reference price to the most extreme price
                    if (TradeDirection == DIRECTION_BUY)
                    {
                        // For BUY grids, reference is the lowest price
                        if (openPrice < ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                    else
                    {
                        // For SELL grids, reference is the highest price
                        if (openPrice > ReferencePrice)
                            ReferencePrice = openPrice;
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check if should open new grid                                   |
//+------------------------------------------------------------------+
bool ShouldOpenNewGrid()
{
    // Don't open if we've reached max grids
    if (CurrentGridCount >= MaxGrids)
        return false;

    // Open first position if none exist
    if (CurrentGridCount == 0)
        return true;

    // Check if price has moved enough for next grid
    double currentPrice = (TradeDirection == DIRECTION_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                         SymbolInfoDouble(Symbol(), SYMBOL_BID);

    if (TradeDirection == DIRECTION_BUY)
    {
        // For BUY: open new grid when price drops below reference - grid size
        return (ReferencePrice - currentPrice) >= GridSizePoints;
    }
    else
    {
        // For SELL: open new grid when price rises above reference + grid size
        return (currentPrice - ReferencePrice) >= GridSizePoints;
    }
}

//+------------------------------------------------------------------+
//| Open new grid position                                          |
//+------------------------------------------------------------------+
void OpenNewGrid()
{
    // Calculate lot size with multiplier
    double lotSize = BaseLotSize * MathPow(LotMultiplier, CurrentGridCount);

    // Determine order type and price
    ENUM_ORDER_TYPE orderType = (TradeDirection == DIRECTION_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    double price = (TradeDirection == DIRECTION_BUY) ?
                   SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                   SymbolInfoDouble(Symbol(), SYMBOL_BID);

    // Calculate Take Profit (same as grid size)
    double tp = 0.0;
    if (TradeDirection == DIRECTION_BUY)
        tp = price + GridSizePoints;
    else
        tp = price - GridSizePoints;

    // Create trade request
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lotSize;
    request.type = orderType;
    request.price = price;
    request.tp = tp;
    request.magic = Magic;
    request.comment = TradeComment;

    if (OrderSend(request, result))
    {
        Print("Grid #", CurrentGridCount + 1, " opened: ", EnumToString(orderType),
              " ", lotSize, " lots at ", price, " TP: ", tp);
    }
    else
    {
        Print("Failed to open grid: ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Check profit target                                             |
//+------------------------------------------------------------------+
bool CheckProfitTarget()
{
    double totalProfit = 0.0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                totalProfit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }

    // Close if profit target reached
    if (totalProfit >= ProfitTargetUSD)
    {
        Print("Profit target reached: $", DoubleToString(totalProfit, 2));
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    int closedCount = 0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                MqlTradeRequest request = {};
                MqlTradeResult result = {};

                request.action = TRADE_ACTION_DEAL;
                request.symbol = Symbol();
                request.volume = PositionGetDouble(POSITION_VOLUME);
                request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
                request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                               SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                               SymbolInfoDouble(Symbol(), SYMBOL_ASK);
                request.magic = Magic;
                request.comment = TradeComment + "_Close";

                if (OrderSend(request, result))
                    closedCount++;
            }
        }
    }

    Print("All ", closedCount, " positions closed - Profit target reached!");

    // Reset grid
    CurrentGridCount = 0;
    ReferencePrice = 0.0;
}

//+------------------------------------------------------------------+
//| Determine trend direction                                        |
//+------------------------------------------------------------------+
int DetermineTrendDirection()
{
    // Simple trend determination using moving averages
    double ma_fast = iMA(Symbol(), PERIOD_M15, 20, 0, MODE_SMA, PRICE_CLOSE, 1);
    double ma_slow = iMA(Symbol(), PERIOD_M15, 50, 0, MODE_SMA, PRICE_CLOSE, 1);

    return (ma_fast > ma_slow) ? POSITION_TYPE_BUY : POSITION_TYPE_SELL;
}

//+------------------------------------------------------------------+
//| Check if should close all positions                             |
//+------------------------------------------------------------------+
bool ShouldCloseAll()
{
    double totalProfit = 0.0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                totalProfit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }

    // Close if profit is positive
    return totalProfit > 0;
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                MqlTradeRequest request = {};
                MqlTradeResult result = {};

                request.action = TRADE_ACTION_DEAL;
                request.symbol = Symbol();
                request.volume = PositionGetDouble(POSITION_VOLUME);
                request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
                request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_BID) : SymbolInfoDouble(Symbol(), SYMBOL_ASK);
                request.magic = Magic;
                request.comment = TradeComment + "_Close";

                OrderSend(request, result);
            }
        }
    }

    Print("All positions closed");

    // Reset grid
    CurrentLegCount = 0;
    ReferencePrice = 0.0;
}