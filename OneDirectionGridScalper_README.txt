OneDirectionGridScalper EA - README
===================================

OVERVIEW:
=========
A pure one-direction grid scalper for MetaTrader 5 that trades BUY-only or SELL-only 
based on user selection. This EA implements a strict "no duplicate rungs" policy and 
uses a single GridSizePips parameter for both spacing and take-profit targets.

KEY FEATURES:
=============
✅ Single Direction Trading: BUY-only or SELL-only (user selects at startup)
✅ No Duplicate Rungs: Never opens multiple positions at the same price level
✅ Single Grid Metric: GridSizePips controls both spacing AND take-profit
✅ Manual Close Logic: Closes trades programmatically when they reach grid size profit
✅ Daily Profit Target: Closes all trades and pauses until next day when equity target hit
✅ Session Time Controls: Only trades during specified hours
✅ Fixed Lot Sizes: No martingale or lot escalation
✅ Maximum Grid Limit: Respects MaxGrids parameter

INPUT PARAMETERS:
=================
Direction         - enum: DIR_BUY or DIR_SELL (locks EA to one direction)
LotSize          - double: 0.10 (fixed lot for every leg)
GridSizePips     - int: 20 (spacing between rungs AND per-leg take-profit)
MaxGrids         - int: 10 (maximum simultaneous positions)
ProfitTargetPct  - double: 5.0 (% equity gain that triggers full close & daily pause)
SessionStartHour - int: 0 (first server-time hour when trading allowed)
SessionEndHour   - int: 23 (last server-time hour when trading allowed)
Magic            - uint: 555555 (unique magic number for this EA instance)

BEHAVIOR FLOW:
==============

1. DAILY RESET (first tick of new server date):
   - Record StartBalance = AccountBalance
   - Reset PausedForDay flag
   - Clear any temporary variables

2. EQUITY PROFIT-STOP (highest priority check):
   - Continuously monitor equity
   - When Equity >= StartBalance × (1 + ProfitTargetPct/100):
     * Close ALL open positions immediately
     * Set PausedForDay = true
     * EA ignores all ticks until next daily reset

3. SESSION GATE:
   - Abort processing if current hour outside [SessionStartHour...SessionEndHour]
   - Abort processing if PausedForDay == true

4. SEEDING:
   - If no live positions exist, open one trade in chosen direction at market
   - Check for duplicate rung before opening

5. PROFIT-LEG RECYCLE (no duplicate rungs):
   - For each open ticket:
     * If price moved +GridSizePips in favor → close it
     * Immediately check if another ticket exists within ±½GridSizePips of current price
     * If none exists AND open-leg count < MaxGrids → open replacement at market
     * If duplicate rung exists → skip opening

6. PULL-BACK ADD-ONS (one per rung rule):
   - Find ReferencePrice = lowest open price (BUY) or highest open price (SELL)
   - If price retraced ≥GridSizePips from ReferencePrice AND open-leg count < MaxGrids:
     * Check for existing ticket within ±½GridSizePips of current price
     * If none → open new leg
     * If duplicate rung → skip

TECHNICAL IMPLEMENTATION:
=========================

RUNG MANAGEMENT:
- Maintains LiveRungs[] array with all occupied price levels
- Updates array on every tick by scanning open positions
- IsRungOccupied() function checks ±½GridSizePips around target price
- Prevents duplicate positions at same price level

GRID SIZE CALCULATION:
- GridSizePoints = GridSizePips × _Point
- For 5-digit/3-digit brokers: GridSizePoints = GridSizePips × _Point × 10
- Used for both spacing calculations and profit targets

TRADE MANAGEMENT:
- Uses CTrade-style OrderSend() with MqlTradeRequest/MqlTradeResult
- Retry logic on TRADE_RETCODE_REQUOTE (once)
- No automatic Take Profit orders - manual close when grid size reached
- Magic number filtering ensures only EA's trades are managed

SAFETY FEATURES:
================
✅ Hard MaxGrids limit enforcement
✅ Direction validation (only opens positions in chosen direction)
✅ Session time filtering
✅ Daily profit target with automatic pause
✅ Duplicate rung prevention
✅ Retry logic for requotes
✅ Comprehensive logging

TESTING CHECKLIST:
==================
Before live trading, verify:
□ Compiles without errors
□ Opens only in chosen direction (BUY or SELL)
□ Respects MaxGrids limit
□ Never opens duplicate rungs
□ Closes positions at grid size profit
□ Stops trading when daily target reached
□ Respects session time controls
□ Uses correct magic number
□ Handles requotes properly

USAGE NOTES:
============
- Set unique Magic number for each EA instance
- Test on demo account first
- Monitor initial trades to verify direction
- Ensure sufficient margin for MaxGrids positions
- Consider spread when setting GridSizePips
- Daily target is based on account equity, not balance

VERSION: 1.00
CREATED: 2025-07-13
COMPATIBILITY: MetaTrader 5
