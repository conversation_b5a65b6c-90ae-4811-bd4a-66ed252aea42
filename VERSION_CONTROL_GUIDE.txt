GridTrendMultiplier EA - Version Control Guide
==============================================

CURRENT FILE STRUCTURE:
========================
✅ GridTrendMultiplier_v1.0_BACKUP.mq5     - STABLE BACKUP VERSION
✅ GridTrendMultiplier_v1.0_BACKUP.ex5     - Compiled backup (WORKING)
✅ GridTrendMultiplier.mq5                 - Current development version
✅ GridTrendMultiplier_main.ex5            - Current compiled version
✅ GridTrendMultiplier_CHANGELOG.txt       - Version history
✅ OneDirectionGridScalper.mq5             - NEW: Pure one-direction scalper
✅ VERSION_CONTROL_GUIDE.txt               - This guide

VERSION NAMING CONVENTION:
==========================
Format: GridTrendMultiplier_v[MAJOR].[MINOR]_[STATUS].mq5

Examples:
- GridTrendMultiplier_v1.0_BACKUP.mq5     (Stable backup)
- GridTrendMultiplier_v1.1_BUGFIX.mq5     (Bug fix version)
- GridTrendMultiplier_v2.0_FEATURE.mq5    (New feature version)
- GridTrendMultiplier_v2.1_DEBUG.mq5      (Debug version)

WORKFLOW FOR CHANGES:
=====================
1. BEFORE making changes:
   - Copy current working version to backup
   - Update changelog with planned changes
   - Test on demo account first

2. DURING development:
   - Make incremental changes
   - Test each change
   - Document issues found

3. AFTER changes:
   - Compile and test thoroughly
   - Update changelog with results
   - Create new backup if stable

BACKUP STRATEGY:
================
ALWAYS KEEP:
- Last known working version as _BACKUP
- Current development version
- Changelog with all changes
- Compiled .ex5 files for quick deployment

RECOVERY PROCEDURE:
===================
If current version fails:
1. Stop EA immediately
2. Load GridTrendMultiplier_v1.0_BACKUP.ex5
3. Check changelog for known issues
4. Revert to last stable version

TESTING PROTOCOL:
=================
1. Compile successfully (no errors)
2. Test on demo account
3. Verify single direction trading
4. Check profit recycling logic
5. Verify daily target functionality
6. Test session time controls

CURRENT STATUS:
===============
✅ v1.0_BACKUP: STABLE - One-way grid with manual close
❌ Current dev: ISSUE - Opening both BUY and SELL simultaneously

NEXT STEPS:
===========
1. Debug why both directions are opening
2. Add more granular logging
3. Test with unique magic numbers
4. Verify no multiple EA instances
5. Create v1.1 when issue resolved

EMERGENCY CONTACTS:
===================
If EA malfunctions:
1. Stop EA immediately
2. Close all positions manually if needed
3. Use backup version
4. Document the issue in changelog

REMEMBER:
=========
- NEVER trade live without testing on demo first
- ALWAYS keep working backups
- DOCUMENT all changes in changelog
- TEST each modification thoroughly
- USE unique magic numbers for different instances
