//+------------------------------------------------------------------+
//| OneDirectionGridScalper.mq5                                      |
//| One-Direction Grid Scalper with No Duplicate Rungs              |
//+------------------------------------------------------------------+
#property copyright "OneDirectionGridScalper"
#property version   "1.00"

//--- Enums
enum ENUM_DIRECTION
{
    DIR_BUY = 0,     // BUY grids only
    DIR_SELL = 1     // SELL grids only
};

//--- Input parameters
input ENUM_DIRECTION Direction = DIR_BUY;    // Grid Direction
input double LotSize = 0.10;                 // Fixed lot for every leg
input int GridSizePips = 20;                 // Distance between entries and per-leg TP
input int MaxGrids = 10;                     // Maximum simultaneous positions
input double ProfitTargetPct = 5.0;          // % equity gain that triggers shutdown
input int SessionStartHour = 0;              // First hour when trading is allowed
input int SessionEndHour = 23;               // Last hour when trading is allowed
input uint Magic = 555555;                   // Unique magic number

//--- Global variables
double GridSizePoints = 0.0;                 // Grid size in points
double StartBalance = 0.0;                   // Starting balance for the day
bool PausedForDay = false;                   // Daily profit target reached flag
datetime LastResetDate = 0;                  // Last daily reset date
double LiveRungs[];                          // Array to track occupied price levels

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Calculate grid size in points
    GridSizePoints = GridSizePips * _Point;
    if (_Digits == 5 || _Digits == 3)
        GridSizePoints = GridSizePips * _Point * 10;

    // Initialize starting balance
    StartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    LastResetDate = TimeCurrent();
    
    // Initialize live rungs array
    ArrayResize(LiveRungs, 0);

    Print("=== OneDirectionGridScalper v1.0 initialized ===");
    Print("*** TRADING DIRECTION: ", EnumToString(Direction), " ONLY ***");
    Print("Magic Number: ", Magic);
    Print("Lot Size: ", LotSize);
    Print("Grid Size: ", GridSizePips, " pips");
    Print("Max Grids: ", MaxGrids);
    Print("Profit Target: ", ProfitTargetPct, "%");
    Print("Session: ", SessionStartHour, ":00 - ", SessionEndHour, ":00");
    Print("Starting Balance: $", DoubleToString(StartBalance, 2));

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("OneDirectionGridScalper EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Equity profit-stop check (highest priority)
    if (CheckEquityProfitTarget())
    {
        CloseAllPositions();
        PausedForDay = true;
        Print("=== EQUITY TARGET REACHED - EA PAUSED UNTIL TOMORROW ===");
        return;
    }

    // Daily reset check
    if (IsNewDay())
    {
        DailyReset();
    }

    // Session gate
    if (!IsWithinSession() || PausedForDay)
    {
        return;
    }

    // Update live rungs array
    UpdateLiveRungs();

    // Seeding - open first trade if no positions exist
    if (ArraySize(LiveRungs) == 0)
    {
        OpenTradeAtMarket();
        return;
    }

    // Profit-leg recycle
    ProcessProfitRecycle();

    // Pull-back add-ons
    CheckPullbackAdds();
}

//+------------------------------------------------------------------+
//| Check if new day for daily reset                                |
//+------------------------------------------------------------------+
bool IsNewDay()
{
    datetime currentTime = TimeCurrent();
    MqlDateTime currentDT, lastDT;

    TimeToStruct(currentTime, currentDT);
    TimeToStruct(LastResetDate, lastDT);

    return (currentDT.day != lastDT.day || currentDT.mon != lastDT.mon || currentDT.year != lastDT.year);
}

//+------------------------------------------------------------------+
//| Daily reset function                                            |
//+------------------------------------------------------------------+
void DailyReset()
{
    PausedForDay = false;
    StartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    LastResetDate = TimeCurrent();

    Print("=== DAILY RESET ===");
    Print("New starting balance: $", DoubleToString(StartBalance, 2));
    Print("EA ready for new trading day");
}

//+------------------------------------------------------------------+
//| Check if within trading session                                 |
//+------------------------------------------------------------------+
bool IsWithinSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;

    if (SessionStartHour <= SessionEndHour)
    {
        return (currentHour >= SessionStartHour && currentHour <= SessionEndHour);
    }
    else
    {
        // Overnight session
        return (currentHour >= SessionStartHour || currentHour <= SessionEndHour);
    }
}

//+------------------------------------------------------------------+
//| Update live rungs array                                         |
//+------------------------------------------------------------------+
void UpdateLiveRungs()
{
    ArrayResize(LiveRungs, 0);
    
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                // Verify position direction matches our setting
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                bool isCorrectDirection = (Direction == DIR_BUY && posType == POSITION_TYPE_BUY) ||
                                        (Direction == DIR_SELL && posType == POSITION_TYPE_SELL);
                
                if (isCorrectDirection)
                {
                    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                    int newSize = ArraySize(LiveRungs) + 1;
                    ArrayResize(LiveRungs, newSize);
                    LiveRungs[newSize - 1] = openPrice;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check equity profit target                                      |
//+------------------------------------------------------------------+
bool CheckEquityProfitTarget()
{
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    double targetEquity = StartBalance * (1.0 + ProfitTargetPct / 100.0);
    
    return (currentEquity >= targetEquity);
}

//+------------------------------------------------------------------+
//| Process profit recycle                                          |
//+------------------------------------------------------------------+
void ProcessProfitRecycle()
{
    double currentPrice = (Direction == DIR_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK);

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                bool isCorrectDirection = (Direction == DIR_BUY && posType == POSITION_TYPE_BUY) ||
                                        (Direction == DIR_SELL && posType == POSITION_TYPE_SELL);
                
                if (!isCorrectDirection) continue;

                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                bool shouldClose = false;

                if (Direction == DIR_BUY)
                {
                    // Close if price moved +GridSizePips above open price
                    shouldClose = (currentPrice - openPrice) >= GridSizePoints;
                }
                else
                {
                    // Close if price moved +GridSizePips below open price  
                    shouldClose = (openPrice - currentPrice) >= GridSizePoints;
                }

                if (shouldClose)
                {
                    ulong ticket = PositionGetTicket(i);
                    ClosePosition(ticket);
                    
                    // Immediately check for replacement if no duplicate rung exists
                    if (!IsRungOccupied(currentPrice) && ArraySize(LiveRungs) < MaxGrids)
                    {
                        OpenTradeAtMarket();
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check pullback adds                                             |
//+------------------------------------------------------------------+
void CheckPullbackAdds()
{
    if (ArraySize(LiveRungs) >= MaxGrids) return;
    
    double currentPrice = (Direction == DIR_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                         SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    // Find reference price (lowest for BUY, highest for SELL)
    double referencePrice = 0.0;
    bool firstRung = true;
    
    for (int i = 0; i < ArraySize(LiveRungs); i++)
    {
        if (firstRung)
        {
            referencePrice = LiveRungs[i];
            firstRung = false;
        }
        else
        {
            if (Direction == DIR_BUY)
            {
                if (LiveRungs[i] < referencePrice)
                    referencePrice = LiveRungs[i];
            }
            else
            {
                if (LiveRungs[i] > referencePrice)
                    referencePrice = LiveRungs[i];
            }
        }
    }
    
    // Check if price has retraced enough for a new add
    bool shouldAdd = false;
    if (Direction == DIR_BUY)
    {
        shouldAdd = (referencePrice - currentPrice) >= GridSizePoints;
    }
    else
    {
        shouldAdd = (currentPrice - referencePrice) >= GridSizePoints;
    }
    
    // Add new position if no duplicate rung exists
    if (shouldAdd && !IsRungOccupied(currentPrice))
    {
        OpenTradeAtMarket();
    }
}

//+------------------------------------------------------------------+
//| Check if rung is occupied                                       |
//+------------------------------------------------------------------+
bool IsRungOccupied(double price)
{
    double halfGrid = GridSizePoints / 2.0;

    for (int i = 0; i < ArraySize(LiveRungs); i++)
    {
        if (MathAbs(LiveRungs[i] - price) <= halfGrid)
        {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Open trade at market                                            |
//+------------------------------------------------------------------+
void OpenTradeAtMarket()
{
    if (ArraySize(LiveRungs) >= MaxGrids)
    {
        Print("Maximum grid count reached: ", MaxGrids);
        return;
    }

    ENUM_ORDER_TYPE orderType = (Direction == DIR_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    double price = (Direction == DIR_BUY) ?
                   SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                   SymbolInfoDouble(Symbol(), SYMBOL_BID);

    // Check if this price level is already occupied
    if (IsRungOccupied(price))
    {
        Print("Rung already occupied at price: ", DoubleToString(price, _Digits));
        return;
    }

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = LotSize;
    request.type = orderType;
    request.price = price;
    request.tp = 0.0;  // No automatic TP - we close manually
    request.sl = 0.0;  // No SL
    request.magic = Magic;
    request.comment = "ODGS_" + EnumToString(Direction);

    bool success = OrderSend(request, result);

    if (!success && result.retcode == TRADE_RETCODE_REQUOTE)
    {
        // Retry once on requote
        Print("Requote received, retrying...");
        Sleep(100);
        success = OrderSend(request, result);
    }

    if (success)
    {
        Print("Position opened: ", EnumToString(orderType), " ", LotSize,
              " lots at ", DoubleToString(price, _Digits));
    }
    else
    {
        Print("Failed to open position. Error: ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Close single position                                           |
//+------------------------------------------------------------------+
void ClosePosition(ulong ticket)
{
    if (!PositionSelectByTicket(ticket))
        return;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = PositionGetString(POSITION_SYMBOL);
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
    request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                   SymbolInfoDouble(request.symbol, SYMBOL_BID) :
                   SymbolInfoDouble(request.symbol, SYMBOL_ASK);
    request.magic = Magic;
    request.comment = "ODGS_Close";

    bool success = OrderSend(request, result);

    if (!success && result.retcode == TRADE_RETCODE_REQUOTE)
    {
        // Retry once on requote
        Sleep(100);
        success = OrderSend(request, result);
    }

    if (success)
    {
        Print("Position closed: Ticket ", ticket, " at grid size profit");
    }
    else
    {
        Print("Failed to close position ", ticket, " Error: ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    int closedCount = 0;
    double totalProfit = 0.0;

    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (PositionGetTicket(i))
        {
            if (PositionGetInteger(POSITION_MAGIC) == Magic &&
                PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                ulong ticket = PositionGetTicket(i);
                totalProfit += PositionGetDouble(POSITION_PROFIT);

                MqlTradeRequest request = {};
                MqlTradeResult result = {};

                request.action = TRADE_ACTION_DEAL;
                request.symbol = Symbol();
                request.volume = PositionGetDouble(POSITION_VOLUME);
                request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
                request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                               SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                               SymbolInfoDouble(Symbol(), SYMBOL_ASK);
                request.magic = Magic;
                request.comment = "ODGS_EquityTarget";

                if (OrderSend(request, result))
                    closedCount++;
            }
        }
    }

    Print("=== EQUITY TARGET REACHED ===");
    Print("Closed ", closedCount, " positions");
    Print("Total profit: $", DoubleToString(totalProfit, 2));
    Print("EA paused until tomorrow");

    // Clear live rungs array
    ArrayResize(LiveRungs, 0);
}
