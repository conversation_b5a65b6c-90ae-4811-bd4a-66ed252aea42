GridTrendMultiplier EA - Version Changelog
==========================================

VERSION 1.0 - BACKUP (GridTrendMultiplier_v1.0_BACKUP.mq5)
===========================================================
Date: 2025-07-13
Status: STABLE BACKUP VERSION

Features:
- One-way grid trading (BUY-only or SELL-only)
- Fixed lot size (no martingale)
- Manual close at grid size profit (no TP orders)
- Daily profit target with pause-until-tomorrow
- Session time controls
- Profit recycling only when positions are profitable
- Pullback adds during drawdown
- Safety checks for direction validation

Input Parameters:
- Direction: DIR_BUY/DIR_SELL (Grid Direction)
- LotSize: 0.10 (Fixed lot for every leg)
- GridSizePips: 20 (Distance between entries and per-leg TP)
- MaxGrids: 10 (Maximum simultaneous positions)
- ProfitTargetPct: 5.0 (% equity gain that triggers shutdown)
- SessionStartHour: 0 (First hour when trading is allowed)
- SessionEndHour: 23 (Last hour when trading is allowed)
- Magic: 555555 (Unique magic number)

Key Functions:
- OnInit(): Initialize EA with grid size calculation
- OnTick(): Main trading logic with session/daily checks
- IsNewDay(): Check for daily reset
- DailyReset(): Reset EA for new trading day
- IsWithinSession(): Validate trading hours
- UpdateGridInfo(): Count positions and track best price
- CheckDailyProfitTarget(): Monitor equity vs target
- ProcessProfitRecycle(): Close profitable positions and reopen
- OpenSeedTrade(): Open first position
- CheckPullbackAdds(): Add positions on retracements
- OpenGridPosition(): Core position opening function
- ClosePosition(): Close single position
- CloseAllPositions(): Close all positions at daily target

Behavior:
1. Opens seed trade in chosen direction
2. Adds positions on pullbacks (GridSizePips retracement)
3. Recycles profitable positions when they reach grid size profit
4. Closes all positions when daily profit target reached
5. Pauses until next day after target reached

VERSION 1.1 - BUGFIX (GridTrendMultiplier_v1.1_BUGFIX.mq5)
============================================================
Date: 2025-07-13
Status: STABLE - FIXED MULTIPLE POSITION ISSUE

CRITICAL BUG FIXED:
- Fixed issue where EA opened multiple BUY/SELL positions simultaneously
- Problem was in profit recycling logic causing race conditions

Changes from v1.0:
- Modified ProcessProfitRecycle() to return bool (prevents multiple actions)
- Added sequential action processing (ONE action per tick)
- Added LastActionTime variable to prevent multiple actions per second
- Improved action prioritization: Seed → Recycle → Pullback
- Added action time recording for all position opening functions

Technical Fixes:
1. ProcessProfitRecycle() now returns true/false
2. OnTick() processes only ONE action per tick
3. Added time-based action throttling
4. Proper sequencing: if recycling happens, skip pullback adds

Behavior Flow (Fixed):
1. Seed trade opens first position ✅
2. Pullback adds new position on retracement ✅
3. When position reaches grid profit → recycle ONLY that position ✅
4. No simultaneous multiple position opening ✅
5. One action per tick maximum ✅

VERSION 2.0 - CURRENT DEVELOPMENT (GridTrendMultiplier.mq5)
===========================================================
Date: 2025-07-13
Status: IN DEVELOPMENT - DEBUGGING MULTIPLE DIRECTION ISSUE

Changes from v1.0:
- Added extensive debugging logs
- Enhanced direction validation
- Added safety checks in position counting
- Improved error messages

Known Issues:
- EA opening both BUY and SELL simultaneously (investigating)
- Need to identify root cause of direction confusion

Debug Features Added:
- Detailed initialization logging
- Position opening confirmation logs
- Direction validation warnings
- Magic number verification

Next Steps:
- Identify why both directions are being opened
- Add more granular debugging
- Ensure only chosen direction trades

VERSION 1.2 - SIMPLE (GridTrendMultiplier_v1.2_SIMPLE.mq5)
============================================================
Date: 2025-07-13
Status: TESTING - REMOVED PROFIT RECYCLING COMPLETELY

MAJOR CHANGE:
- Completely removed profit recycling logic
- Now just closes positions at grid profit (no reopening)
- Back to basics approach to eliminate multiple position bug

Changes from v1.1:
- Removed ProcessProfitRecycle() function entirely
- Added CloseGridProfitPositions() - just closes, no recycling
- Simplified OnTick() logic
- Enhanced logging for debugging
- Clear separation of actions

New Behavior:
1. Opens seed trade in chosen direction ✅
2. Adds positions on pullbacks (GridSizePips retracement) ✅
3. Closes positions when they reach grid size profit ✅
4. NO RECYCLING - positions just close and stay closed ✅
5. Daily target closes all positions ✅

This should eliminate the multiple position issue by removing
the complex recycling logic that was causing race conditions.

BACKUP STRATEGY:
================
- v1.0_BACKUP: Original stable version
- v1.1_BUGFIX: Attempted fix with action sequencing
- v1.2_SIMPLE: No recycling approach (TESTING)
- Always test on demo before live trading
- Keep backups of working versions

FILE STRUCTURE:
===============
- GridTrendMultiplier.mq5 (Current development version)
- GridTrendMultiplier_v1.0_BACKUP.mq5 (Stable backup)
- GridTrendMultiplier_CHANGELOG.txt (This file)
- GridTrendMultiplier.ex5 (Compiled current version)
- GridTrendMultiplier_v1.0_BACKUP.ex5 (Will be created when needed)
